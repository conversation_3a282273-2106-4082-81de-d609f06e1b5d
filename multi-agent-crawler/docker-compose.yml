version: '3.8'

services:
  # NocoDB - Latest Version with Cosmic Quality Configuration
  nocodb:
    image: nocodb/nocodb:latest
    container_name: nocodb_cosmic
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - NC_DB=sqlite3://data/noco.db
      - NC_AUTH_JWT_SECRET=cosmic-quality-jwt-secret-2025
      - NC_PUBLIC_URL=http://localhost:8080
      - NC_DISABLE_TELE=true
      - NC_ADMIN_EMAIL=<EMAIL>
      - NC_ADMIN_PASSWORD=CosmicQuality2025!
      - NC_TOOL_DIR=/usr/app/data/tools
      - NC_DASHBOARD_URL=http://localhost:8080/dashboard
    volumes:
      - nocodb_data:/usr/app/data
      - ./shared/nocodb-config:/usr/app/config
    networks:
      - crawler_network
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '1.5'
        reservations:
          memory: 1G
          cpus: '0.75'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v1/db/meta/projects"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis 7+ with Cosmic Quality Optimization
  redis:
    image: redis:7.2-alpine
    container_name: redis_cosmic
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: >
      redis-server
      --maxmemory 2gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
      --appendonly yes
      --appendfsync everysec
      --tcp-keepalive 300
      --timeout 0
      --databases 16
      --maxclients 10000
    volumes:
      - redis_data:/data
      - ./shared/redis-config:/usr/local/etc/redis
    networks:
      - crawler_network
    deploy:
      resources:
        limits:
          memory: 2.5G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Cosmic Quality Multi-Agent Crawler Orchestrator
  crawler-orchestrator:
    build:
      context: ./crawler-orchestrator
      dockerfile: Dockerfile
    container_name: cosmic_orchestrator
    restart: unless-stopped
    ports:
      - "8000:8000"  # FastAPI REST API
      - "8001:8001"  # Gradio interface
      - "8003:8003"  # Streamlit control panel
      - "8004:8004"  # Tablet-optimized interface
      - "8005:8005"  # Cosmic Streamlit interface
    environment:
      # API Keys
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - EXA_API_KEY=${EXA_API_KEY}
      - BRAVE_API_KEY=${BRAVE_API_KEY}

      # Service URLs
      - NOCODB_URL=http://nocodb_cosmic:8080
      - REDIS_URL=redis://redis_cosmic:6379
      - CRAWL4AI_URL=http://crawl4ai_cosmic:8002

      # Python Configuration
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1

      # Cosmic Quality Performance Settings
      - MAX_CONCURRENT_CRAWLS=3
      - CRAWL_TIMEOUT=180
      - CACHE_TTL=7200
      - MEMORY_LIMIT=3.5G
      - CPU_LIMIT=1.8

      # LLM Configuration
      - DEFAULT_MODEL=google/gemma-2-9b-it
      - FALLBACK_MODEL=google/gemma-2-27b-it
      - COMPLEX_TASK_MODEL=google/gemma-2-27b-it
      - EMBEDDING_MODEL=text-embedding-3-small

      # Cosmic Quality Standards
      - QUALITY_LEVEL=cosmic
      - TARGET_ACCURACY=0.999
      - MAX_RESPONSE_TIME_MS=1000
      - PRESENTATION_LEVEL=corporation
      - DATA_VALIDATION_LEVEL=comprehensive
      - PERFORMANCE_OPTIMIZATION=true
      - REAL_TIME_MONITORING=true

    volumes:
      - ./crawler-orchestrator:/app
      - crawler_data:/app/data
      - ./shared:/app/shared
      - ./cosmic-config:/app/cosmic-config
    depends_on:
      - nocodb
      - redis
      - crawl4ai
    networks:
      - crawler_network
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2.5G
          cpus: '1.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Cosmic Quality Crawl4AI Service
  crawl4ai:
    build:
      context: ./crawl4ai-service
      dockerfile: Dockerfile
    container_name: crawl4ai_cosmic
    restart: unless-stopped
    ports:
      - "8002:8002"
    environment:
      - REDIS_URL=redis://redis_cosmic:6379
      - MAX_CONCURRENT_CRAWLS=3
      - CRAWL_TIMEOUT=180
      - BROWSER_TIMEOUT=120
      - PAGE_LOAD_TIMEOUT=60
      - EXTRACTION_TIMEOUT=30
      - QUALITY_LEVEL=cosmic
      - ENABLE_JAVASCRIPT=true
      - ENABLE_SCREENSHOTS=true
      - ENABLE_PDF_EXTRACTION=true
      - USER_AGENT=CosmicRealEstate/1.0 (Professional Real Estate Intelligence)
      - STEALTH_MODE=true
      - RESPECT_ROBOTS_TXT=true
      - RATE_LIMIT_DELAY=2
    volumes:
      - crawl4ai_data:/app/data
      - ./shared/crawl4ai-config:/app/config
    depends_on:
      - redis
    networks:
      - crawler_network
    deploy:
      resources:
        limits:
          memory: 3.5G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 90s

  # Cosmic Quality Task Scheduler
  scheduler:
    build:
      context: ./scheduler
      dockerfile: Dockerfile
    container_name: cosmic_scheduler
    restart: unless-stopped
    environment:
      - REDIS_URL=redis://redis_cosmic:6379
      - NOCODB_URL=http://nocodb_cosmic:8080
      - SCHEDULE_INTERVAL=300
      - CLEANUP_INTERVAL=3600
      - PERFORMANCE_MONITORING=true
      - LOG_LEVEL=INFO
      - COSMIC_QUALITY=true
    volumes:
      - ./scheduler:/app
      - ./shared/scheduler-config:/app/config
      - scheduler_logs:/app/logs
    depends_on:
      - redis
      - nocodb
    networks:
      - crawler_network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.75'
        reservations:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health', timeout=5)"]
      interval: 60s
      timeout: 10s
      retries: 3

volumes:
  # Data volumes
  nocodb_data:
    driver: local
  redis_data:
    driver: local
  crawler_data:
    driver: local
  crawl4ai_data:
    driver: local
  scheduler_logs:
    driver: local

  # Configuration volumes
  cosmic_config:
    driver: local
  shared_config:
    driver: local

networks:
  crawler_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
