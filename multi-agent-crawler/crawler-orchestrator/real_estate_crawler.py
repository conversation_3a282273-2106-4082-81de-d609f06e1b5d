from src.agents.real_estate_agents import (
    create_real_estate_researcher,
    create_presentation_designer,
    create_market_analyst,
    create_client_specialist
)
import argparse
import os
import sys
from datetime import datetime
from dotenv import load_dotenv
from crewai import Crew, Process
from loguru import logger
from .services.llm_service import LLMService
from .services.redis_service import RedisService
from .agents.real_estate_crew import RealEstateCrew

# Create agents
researcher = create_real_estate_researcher()
presentation_designer = create_presentation_designer()
analyst = create_market_analyst()
client_specialist = create_client_specialist()

# Create crew
real_estate_crew = Crew(
    agents=[researcher, presentation_designer, analyst, client_specialist],
    process=Process.sequential,
    verbose=2
)

# Run the crew
def run_real_estate_search(location, min_price, max_price):
    """Run the real estate property search and presentation creation process."""
    result = real_estate_crew.kickoff(inputs={
        'location': location,
        'min_price': min_price,
        'max_price': max_price
    })
    return result

# Example usage
if __name__ == "__main__":
    result = run_real_estate_search("Beverly Hills", 5000000, 20000000)
    print("\n\n======================")
    print("Final Result")
    print("======================\n")
    print(result)
def parse_arguments():
    parser = argparse.ArgumentParser(description='Real Estate Data Crawler')
    parser.add_argument(
        '--region',
        type=str,
        default='Poland',
        help='Target region for data gathering (default: Poland)'
    )
    parser.add_argument(
        '--max-sites',
        type=int,
        default=20,
        help='Maximum number of sites to crawl (default: 20)'
    )
    parser.add_argument(
        '--output-dir',
        type=str,
        default='./data',
        help='Directory to store output data (default: ./data)'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose output'
    )
    return parser.parse_args()

def setup_environment():
    """Set up the environment for the crawler."""
    # Load environment variables
    load_dotenv()
    
    # Check for required environment variables
    required_vars = ['OPENAI_API_KEY', 'REDIS_URL']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please set these variables in your .env file or environment")
        sys.exit(1)
    
    # Create output directory if it doesn't exist
    args = parse_arguments()
    os.makedirs(args.output_dir, exist_ok=True)
    
    return args

def main():
    """Main function to run the real estate data crawler."""
    try:
        # Set up environment and parse arguments
        args = setup_environment()
        
        # Configure logging level based on verbose flag
        if args.verbose:
            logger.level("DEBUG")
        
        logger.info("Starting Real Estate Data Crawler")
        logger.info(f"Target Region: {args.region}")
        logger.info(f"Max Sites: {args.max_sites}")
        logger.info(f"Output Directory: {args.output_dir}")
        
        # Initialize services
        llm_service = LLMService()
        redis_service = RedisService()
        
        # Create and run the real estate crew
        start_time = datetime.now()
        logger.info(f"Crawler started at: {start_time}")
        
        crew = RealEstateCrew(llm_service=llm_service)
        result = crew.run(target_region=args.region, max_sites=args.max_sites)
        
        # Save results
        end_time = datetime.now()
        duration = end_time - start_time
        
        timestamp = end_time.strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(args.output_dir, f"real_estate_data_{timestamp}.json")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result)
        
        logger.info(f"Crawler completed in: {duration}")
        logger.info(f"Results saved to: {output_file}")
        
        return 0
    
    except KeyboardInterrupt:
        logger.info("Crawler interrupted by user")
        return 1
    
    except Exception as e:
        logger.exception(f"Error running crawler: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
